#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音符数据模型
定义音符信息结构和完整的音符映射表
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
import math

@dataclass
class NoteInfo:
    """音符信息数据类"""
    pitch: str           # 音高标记，如 'C4'
    position: str        # 在五线谱上的位置，如 'ledger_-1'
    frequency: float     # 频率(Hz)
    note_name: str       # 音名，如 'C'
    solfege: str         # 唱名，如 'do'
    is_black_key: bool   # 是否为黑键
    octave: int          # 八度，如 4
    staff_y: int         # 在五线谱上的Y坐标位置（相对于第三线）

class NoteDatabase:
    """音符数据库类"""
    
    def __init__(self):
        """初始化音符数据库"""
        self.notes: Dict[str, NoteInfo] = {}
        self.white_keys: List[str] = []
        self.black_keys: List[str] = []
        self._initialize_notes()
    
    def _initialize_notes(self):
        """初始化所有音符数据"""
        # 音符基础数据：音名、唱名、是否为黑键
        note_data = [
            # A3
            ('A3', 'A', 'la', False, 3, 220.00, 'ledger_-2'),
            ('A#3', 'A#', '', True, 3, 233.08, ''),
            ('B3', 'B', 'si', False, 3, 246.94, 'ledger_-1.5'),
            
            # C4 (中央C)
            ('C4', 'C', 'do', False, 4, 261.63, 'ledger_-1'),
            ('C#4', 'C#', '', True, 4, 277.18, ''),
            ('D4', 'D', 're', False, 4, 293.66, 'space_below_1'),
            ('D#4', 'D#', '', True, 4, 311.13, ''),
            ('E4', 'E', 'mi', False, 4, 329.63, 'line_1'),
            ('F4', 'F', 'fa', False, 4, 349.23, 'space_1'),
            ('F#4', 'F#', '', True, 4, 369.99, ''),
            ('G4', 'G', 'sol', False, 4, 392.00, 'line_2'),
            ('G#4', 'G#', '', True, 4, 415.30, ''),
            ('A4', 'A', 'la', False, 4, 440.00, 'space_2'),
            ('A#4', 'A#', '', True, 4, 466.16, ''),
            ('B4', 'B', 'si', False, 4, 493.88, 'line_3'),
            
            # C5
            ('C5', 'C', 'do', False, 5, 523.25, 'space_3'),
            ('C#5', 'C#', '', True, 5, 554.37, ''),
            ('D5', 'D', 're', False, 5, 587.33, 'line_4'),
            ('D#5', 'D#', '', True, 5, 622.25, ''),
            ('E5', 'E', 'mi', False, 5, 659.25, 'space_4'),
            ('F5', 'F', 'fa', False, 5, 698.46, 'line_5'),
            ('F#5', 'F#', '', True, 5, 739.99, ''),
            ('G5', 'G', 'sol', False, 5, 783.99, 'space_above_5'),
            ('G#5', 'G#', '', True, 5, 830.61, ''),
            ('A5', 'A', 'la', False, 5, 880.00, 'ledger_1'),
            ('A#5', 'A#', '', True, 5, 932.33, ''),
            ('B5', 'B', 'si', False, 5, 987.77, 'ledger_1.5'),
            
            # C6
            ('C6', 'C', 'do', False, 6, 1046.50, 'ledger_2'),
        ]
        
        # 计算五线谱Y坐标位置
        position_y_map = {
            'ledger_-2': -4,      # 下加二线
            'ledger_-1.5': -3,    # 下加一线和下加二线之间
            'ledger_-1': -2,      # 下加一线
            'space_below_1': -1,  # 下加一线和第一线之间
            'line_1': 0,          # 第一线
            'space_1': 1,         # 第一间
            'line_2': 2,          # 第二线
            'space_2': 3,         # 第二间
            'line_3': 4,          # 第三线（基准线）
            'space_3': 5,         # 第三间
            'line_4': 6,          # 第四线
            'space_4': 7,         # 第四间
            'line_5': 8,          # 第五线
            'space_above_5': 9,   # 第五线上方
            'ledger_1': 10,       # 上加一线
            'ledger_1.5': 11,     # 上加一线和上加二线之间
            'ledger_2': 12,       # 上加二线
        }
        
        # 创建音符对象
        for pitch, note_name, solfege, is_black_key, octave, frequency, position in note_data:
            staff_y = position_y_map.get(position, 4)  # 默认为第三线
            
            note_info = NoteInfo(
                pitch=pitch,
                position=position,
                frequency=frequency,
                note_name=note_name,
                solfege=solfege,
                is_black_key=is_black_key,
                octave=octave,
                staff_y=staff_y
            )
            
            self.notes[pitch] = note_info
            
            if is_black_key:
                self.black_keys.append(pitch)
            else:
                self.white_keys.append(pitch)
    
    def get_note(self, pitch: str) -> Optional[NoteInfo]:
        """根据音高获取音符信息"""
        return self.notes.get(pitch)
    
    def get_all_notes(self) -> List[NoteInfo]:
        """获取所有音符信息"""
        return list(self.notes.values())
    
    def get_white_keys(self) -> List[NoteInfo]:
        """获取所有白键音符"""
        return [self.notes[pitch] for pitch in self.white_keys]
    
    def get_black_keys(self) -> List[NoteInfo]:
        """获取所有黑键音符"""
        return [self.notes[pitch] for pitch in self.black_keys]
    
    def get_notes_by_octave(self, octave: int) -> List[NoteInfo]:
        """根据八度获取音符"""
        return [note for note in self.notes.values() if note.octave == octave]
    
    def get_random_white_key(self) -> NoteInfo:
        """随机获取一个白键音符"""
        import random
        pitch = random.choice(self.white_keys)
        return self.notes[pitch]

# 全局音符数据库实例
note_db = NoteDatabase()
